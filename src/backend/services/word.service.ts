import { WordRepository } from '@/backend/repositories';
import { CollectionService, LastSeenWordService } from '@/backend/services';
import { LLMService } from '@/backend/services/llm.service';
import { RandomWordDetail, WordDetail } from '@/models';
import { Language, LastSeenWord, Prisma, Word, Definition } from '@prisma/client';
import { AuditHelper } from '@/backend/utils/audit.helper';
import { AUDIT_ACTIONS } from './audit.service';

export interface WordService {
	searchWords(term: string, language?: Language, limit?: number): Promise<WordDetail[]>;
	getWordById(wordId: string): Promise<WordDetail | null>;
	findWordsByIds(wordIds: string[]): Promise<WordDetail[]>;
	getWordsByTerms(terms: string[], language?: Language): Promise<WordDetail[]>;
	getWordsByCollection(userId: string, collectionId: string): Promise<WordDetail[]>;
	searchWordsInCollection(
		userId: string,
		collectionId: string,
		term: string,
		language?: Language,
		limit?: number
	): Promise<WordDetail[]>;
	getWordsToReview(userId: string, collectionId: string, limit?: number): Promise<WordDetail[]>;
	createWordWithRandomWordDetail(
		word: RandomWordDetail,
		userId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<WordDetail>;
	addExamplesToWord(
		wordId: string,
		examples: Array<{ EN: string; VI: string }>
	): Promise<WordDetail>;
	bulkDeleteWordsFromCollection(
		userId: string,
		collectionId: string,
		wordIds: string[]
	): Promise<number>;
	findWordsWithoutDefinitions(language?: Language, limit?: number): Promise<WordDetail[]>;
	deleteWordsWithoutDefinitions(
		language?: Language,
		batchSize?: number
	): Promise<{ deletedCount: number; affectedCollections: number }>;

	// New methods for example pagination
	getMoreExamples(
		wordId: string,
		definitionId: string,
		offset: number,
		limit?: number
	): Promise<{
		examples: Array<{ id: string; EN: string; VI: string }>;
	}>;
	generateAndSaveExamples(
		wordId: string,
		definitionId: string,
		count?: number
	): Promise<Array<{ id: string; EN: string; VI: string }>>;
}

export class WordServiceImpl implements WordService {
	// Mutex để tránh race condition khi generate examples
	private exampleGenerationLocks = new Map<string, Promise<any>>();

	constructor(
		private readonly getWordRepository: () => WordRepository,
		private readonly getCollectionService: () => CollectionService,
		private readonly getLastSeenWordService: () => LastSeenWordService,
		private readonly getAuditHelper: () => AuditHelper,
		private readonly getLLMService: () => Promise<LLMService>
	) {}

	// Enrich a WordDetail - WordNet data is already included from database query
	private async _enrichWord(word: Word): Promise<WordDetail> {
		return {
			...(word as WordDetail),
		};
	}

	// Link a word to existing WordNetData if available
	private async _linkWordToWordNetData(wordId: string, term: string): Promise<void> {
		try {
			// Find the best matching WordNetData entry for this term
			const wordNetData = await this._findBestWordNetDataMatch(term);

			if (wordNetData) {
				// Update the word to link it to the WordNetData
				await this.getWordRepository().update(wordId, {
					WordNetData: {
						connect: {
							id: wordNetData.id,
						},
					},
				});
				console.log(`✅ Linked word "${term}" to WordNetData entry`);
			}
		} catch (error) {
			console.error(`Failed to link word "${term}" to WordNetData:`, error);
			// Don't throw error - this is not critical for word creation
		}
	}

	// Find the best matching WordNetData entry for a term
	private async _findBestWordNetDataMatch(term: string): Promise<{ id: string } | null> {
		try {
			// Query WordNetData table directly to find matching entries
			const wordRepository = this.getWordRepository();
			if ('prisma' in wordRepository) {
				const prisma = (wordRepository as any).prisma;
				const wordNetEntry = await prisma.wordNetData.findFirst({
					where: {
						term: term.toLowerCase(),
					},
					select: {
						id: true,
					},
				});

				return wordNetEntry;
			}

			return null;
		} catch (error) {
			console.error(`Error finding WordNetData for term "${term}":`, error);
			return null;
		}
	}

	async searchWords(term: string, language?: Language, limit = 10): Promise<WordDetail[]> {
		let words: Word[];

		if (term === '') {
			// If term is empty, get all words (potentially filtered by language and limited)
			const whereClause: Prisma.WordWhereInput = {};
			if (language) {
				whereClause.language = language;
			}
			words = await this.getWordRepository().find(whereClause, limit);
		} else {
			// Search words normally
			words = await this.getWordRepository().searchWords(term, language, limit);
		}

		return Promise.all(words.map((w) => this._enrichWord(w)));
	}

	async searchWordsInCollection(
		userId: string,
		collectionId: string,
		term: string,
		language?: Language,
		limit?: number
	): Promise<WordDetail[]> {
		// 1. Get the collection to find the list of word IDs.
		// This step still requires the CollectionService to get the collection structure.
		const collection = await this.getCollectionService().getCollectionById(
			userId,
			collectionId
		);

		if (!collection || collection.word_ids.length === 0) {
			// If collection doesn't exist or is empty, there are no words to search within.
			return [];
		}

		const collectionWordIds = collection.word_ids;

		// 2. Construct the Prisma query to search within these specific word IDs.
		// This pushes the filtering logic (term, language, and ID constraint) down to the repository/database.
		const whereClause: Prisma.WordWhereInput = {
			id: {
				in: collectionWordIds,
			},
		};

		// Add term filter if provided. Replicate case-insensitive 'contains' logic used elsewhere.
		if (term) {
			whereClause.term = {
				contains: term,
				mode: 'insensitive', // Assuming Prisma client is configured for case-insensitive search
			};
		}

		// Add language filter if provided.
		if (language) {
			whereClause.language = language;
		}

		// 3. Use the repository's find method directly with the constructed query and limit.
		// We assume the repository's `find` method accepts Prisma FindManyOptions like { where, take, orderBy }.
		// If the actual repository only accepts 'where', the limit would need to be applied in memory here,
		// but using 'take' in the repository call is the standard and efficient way to handle limits in SQL via Prisma.
		const words = await this.getWordRepository().find(whereClause, limit);

		// 4. Enrich the retrieved words using the existing private method.
		return Promise.all(words.map((w) => this._enrichWord(w)));
	}

	async getWordById(wordId: string): Promise<WordDetail | null> {
		const word = await this.getWordRepository().findById(wordId);
		if (!word) return null;
		return this._enrichWord(word);
	}

	async findWordsByIds(wordIds: string[]): Promise<WordDetail[]> {
		const words = await this.getWordRepository().findWordsByIds(wordIds);
		return Promise.all(words.map((w) => this._enrichWord(w)));
	}

	async getWordsByTerms(terms: string[], language?: Language): Promise<WordDetail[]> {
		const query: Prisma.WordWhereInput = {
			term: {
				in: terms,
				mode: 'insensitive',
			},
		};
		if (language) {
			query.language = language;
		}
		const words = await this.getWordRepository().find(query);
		return Promise.all(words.map((w) => this._enrichWord(w)));
	}

	async getWordsByCollection(userId: string, collectionId: string): Promise<WordDetail[]> {
		const collection = await this.getCollectionService().getCollectionById(
			userId,
			collectionId
		);
		if (!collection) {
			throw new Error(`Collection with ID ${collectionId} not found`);
		}

		const words = await this.getWordRepository().findWordsByIds(collection.word_ids);
		return Promise.all(words.map((w) => this._enrichWord(w)));
	}

	async getWordsToReview(
		userId: string,
		collectionId: string,
		limit: number = 20
	): Promise<WordDetail[]> {
		const collectionWords = await this.getWordsByCollection(userId, collectionId);
		const collectionWordIds = collectionWords.map((w) => w.id);

		if (collectionWords.length === 0) {
			// No words in the collection, return empty array.
			return [];
		}

		// Fetch last seen entries for the words in the collection
		const userLastSeenWords = await this.getLastSeenWordService().findLastSeenWordsByWordIds(
			userId,
			collectionWordIds
		);

		// Create a map for quicker lookup of last seen data by word ID
		const lastSeenMap = new Map<string, LastSeenWord>();
		for (const ls of userLastSeenWords) {
			lastSeenMap.set(ls.word_id, ls);
		}

		const now = new Date();

		// Combine word details with their last seen data and calculate a review priority score
		const wordsWithReviewData = collectionWords.map((word) => {
			let lastSeen = lastSeenMap.get(word.id);

			// If a word doesn't have a last seen entry, treat it as never seen (last_seen_at = epoch, review_count = 0)
			if (!lastSeen) {
				// Create a temporary object representing the 'never seen' state for calculation purposes
				lastSeen = {
					id: 'temp-' + word.id, // Use a temporary ID
					word_id: word.id,
					user_id: userId,
					last_seen_at: new Date(0), // Epoch time represents never seen
					review_count: 0,
				};
			}

			const timePassed = now.getTime() - lastSeen.last_seen_at.getTime();
			// Calculate days passed, handle potential negative values if system clock is messed up (though Date(0) makes this unlikely)
			const daysPassed = Math.max(0, timePassed / (1000 * 60 * 60 * 24));

			const reviewCount = lastSeen.review_count || 0; // Default to 0 if somehow null/undefined
			// Calculate the next expected review interval based on the review count using the spaced repetition formula
			// Interval = 2^review_count * base_interval_in_days (base interval is 1 day)
			const reviewInterval = Math.pow(2, reviewCount) * 1; // Interval in days

			// Calculate a numerical priority score for sorting.
			// A positive score means it's overdue (daysPassed > reviewInterval).
			// A score of zero means it's exactly due.
			// A negative score means it's not due yet (daysPassed < reviewInterval).
			// Words never seen (last_seen_at = epoch) will have a very large daysPassed, resulting in a large positive score.
			// Sorting descending by this score will put the most overdue/never seen words first.
			const reviewPriority = daysPassed - reviewInterval;

			return {
				word: word,
				lastSeenAt: lastSeen.last_seen_at, // Include last seen time for secondary sort
				reviewPriority: reviewPriority,
			};
		});

		// Sort the words based on the calculated review priority.
		// Primary sort: descending by reviewPriority (most overdue/never seen first).
		// Secondary sort: ascending by lastSeenAt (for words with the same priority, those seen longest ago come first).
		wordsWithReviewData.sort((a, b) => {
			// Sort by priority descending
			if (b.reviewPriority !== a.reviewPriority) {
				return b.reviewPriority - a.reviewPriority;
			}
			// If priority is the same, sort by last seen time ascending (oldest first)
			return a.lastSeenAt.getTime() - b.lastSeenAt.getTime();
		});

		// Apply the limit to the sorted list
		const limitedWordsWithReviewData = wordsWithReviewData.slice(0, limit);

		// Extract the original WordDetail objects from the sorted and limited list
		const wordsToReview = limitedWordsWithReviewData.map((item) => item.word);

		return wordsToReview;
	}

	async createWordWithRandomWordDetail(
		word: RandomWordDetail,
		userId?: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<WordDetail> {
		const createInput: Prisma.WordCreateInput = {
			term: word.term,
			language: word.language,
			definitions: {
				create: word.definitions.map((def) => ({
					pos: def.pos,
					ipa: def.ipa,
					explains: { create: def.explains },
					examples: { create: def.examples },
				})),
			},
		};

		try {
			const created = await this.getWordRepository().create(createInput);

			// Link to existing WordNetData if available (English only)
			if (word.language === Language.EN) {
				await this._linkWordToWordNetData(created.id, word.term);
			}

			const enrichedWord = await this._enrichWord(created);

			// Log audit event
			try {
				const auditHelper = this.getAuditHelper();
				await auditHelper.logWordEvent(
					AUDIT_ACTIONS.WORD_CREATED,
					created.id,
					userId,
					{
						term: word.term,
						language: word.language,
						definitions_count: word.definitions.length,
						total_examples: word.definitions.reduce(
							(sum, def) => sum + def.examples.length,
							0
						),
						total_explanations: word.definitions.reduce(
							(sum, def) => sum + def.explains.length,
							0
						),
					},
					{ ip_address: ipAddress, user_agent: userAgent }
				);
			} catch (error) {
				console.error('Failed to log audit event for word creation:', error);
			}

			return enrichedWord;
		} catch (error: any) {
			// If unique constraint failed, try to find existing word
			if (error.code === 'P2002') {
				const existingWord = await this.getWordRepository().findByTerm(
					word.term,
					word.language
				);
				if (existingWord) {
					// Also try to link existing word to WordNetData if not already linked
					if (word.language === Language.EN && !existingWord.wordNetDataId) {
						await this._linkWordToWordNetData(existingWord.id, word.term);
						// Refetch the word to get the updated WordNetData relationship
						const updatedWord = await this.getWordRepository().findById(
							existingWord.id
						);
						if (updatedWord) {
							return this._enrichWord(updatedWord);
						}
					}
					return this._enrichWord(existingWord);
				}
			}
			throw error;
		}
	}

	async addExamplesToWord(
		wordId: string,
		examples: Array<{ EN: string; VI: string }>
	): Promise<WordDetail> {
		// Get the word with its definitions
		const word = (await this.getWordRepository().findById(wordId)) as
			| (Word & { definitions: Definition[] })
			| null;
		if (!word) {
			throw new Error(`Word with ID ${wordId} not found`);
		}

		// Get the first definition, or create one if none exists
		let definitionId: string;
		if (word.definitions && word.definitions.length > 0) {
			definitionId = word.definitions[0].id;
		} else {
			// Create a basic definition if none exists
			const newDefinition = await this.getWordRepository().createDefinition({
				word: { connect: { id: wordId } },
				pos: [],
				ipa: '',
				images: [],
			});
			definitionId = newDefinition.id;
		}

		// Add examples to the definition
		await this.getWordRepository().addExamplesToDefinition(definitionId, examples);

		// Return the updated word
		const updatedWord = await this.getWordRepository().findById(wordId);
		return this._enrichWord(updatedWord!);
	}

	/**
	 * Bulk delete words from a collection.
	 * @param userId - The ID of the user who owns the collection
	 * @param collectionId - The ID of the collection to remove words from
	 * @param wordIds - Array of word IDs to remove from the collection
	 * @returns Promise<number> - The number of words actually removed from the collection
	 * @throws {Error} If there's an error during the removal process
	 */
	async bulkDeleteWordsFromCollection(
		userId: string,
		collectionId: string,
		wordIds: string[]
	): Promise<number> {
		if (!wordIds || wordIds.length === 0) {
			return 0; // No words to remove
		}

		try {
			// Get the collection before modification to count initial words
			const collectionBefore = await this.getCollectionService().getCollectionById(
				userId,
				collectionId
			);

			if (!collectionBefore) {
				throw new Error(`Collection with ID ${collectionId} not found`);
			}

			const wordIdsToRemove = new Set(wordIds);
			const originalWordCount = collectionBefore.word_ids.filter((id) =>
				wordIdsToRemove.has(id)
			).length;

			const result = await this.getCollectionService().removeWordsFromCollection(
				userId,
				collectionId,
				wordIds
			);

			// If result is null, it means the collection was not found or user doesn't have access
			if (result === null) {
				throw new Error(`Collection with ID ${collectionId} not found`);
			}

			// Return the count of words that were actually in the collection and got removed
			return originalWordCount;
		} catch (error) {
			throw new Error(
				`Failed to bulk delete words from collection: ${
					error instanceof Error ? error.message : 'Unknown error'
				}`
			);
		}
	}

	async findWordsWithoutDefinitions(language?: Language, limit?: number): Promise<WordDetail[]> {
		const whereClause: Prisma.WordWhereInput = {
			definitions: {
				none: {},
			},
		};

		if (language) {
			whereClause.language = language;
		}

		const words = await this.getWordRepository().find(whereClause, limit);
		return Promise.all(words.map((w) => this._enrichWord(w)));
	}

	async deleteWordsWithoutDefinitions(
		language?: Language,
		batchSize: number = 100
	): Promise<{ deletedCount: number; affectedCollections: number }> {
		const wordsToDelete = await this.findWordsWithoutDefinitions(language);

		if (wordsToDelete.length === 0) {
			return { deletedCount: 0, affectedCollections: 0 };
		}

		const wordIds = wordsToDelete.map((w) => w.id);
		let deletedCount = 0;
		const affectedCollections = 0;

		// Process in batches to avoid database limits
		for (let i = 0; i < wordIds.length; i += batchSize) {
			const batch = wordIds.slice(i, i + batchSize);

			// Use repository delete method
			await this.getWordRepository().delete({ id: { in: batch } });
			deletedCount += batch.length;
		}

		return { deletedCount, affectedCollections };
	}

	async getMoreExamples(
		wordId: string,
		definitionId: string,
		offset: number,
		limit: number = 3
	): Promise<{
		examples: Array<{ id: string; EN: string; VI: string }>;
	}> {
		const wordRepository = this.getWordRepository();

		// Get current count of examples for this definition in database
		const dbCount = await wordRepository.countExamplesByDefinition(definitionId);
		const maxExamples = 20;

		// Get examples from database first
		const dbExamples = await wordRepository.getExamplesByDefinition(
			definitionId,
			offset,
			limit
		);

		// Check if we have enough examples from database
		if (dbExamples.length >= limit) {
			return {
				examples: dbExamples,
			};
		}

		// If we don't have enough examples in DB and haven't reached max (20), generate more
		if (dbCount < maxExamples && dbExamples.length < limit) {
			const neededCount = Math.min(limit - dbExamples.length, maxExamples - dbCount);
			if (neededCount > 0) {
				try {
					// Generate examples để fill database đến đủ cho request này và future requests
					await this.generateAndSaveExamples(wordId, definitionId, neededCount);

					// Sau khi generate, fetch lại examples từ database với offset chính xác
					const freshExamples = await wordRepository.getExamplesByDefinition(
						definitionId,
						offset,
						limit
					);

					return {
						examples: freshExamples,
					};
				} catch (error) {
					console.error('Failed to generate examples:', error);
					// Return what we have from DB
					return {
						examples: dbExamples,
					};
				}
			}
		}

		return {
			examples: dbExamples,
		};
	}

	async generateAndSaveExamples(
		wordId: string,
		definitionId: string,
		count: number = 3
	): Promise<Array<{ id: string; EN: string; VI: string }>> {
		// Tạo unique key cho definition để lock
		const lockKey = `generate-examples-${definitionId}`;

		// Check if đã có lock cho definition này
		if (this.exampleGenerationLocks.has(lockKey)) {
			// Nếu có lock, đợi lock trước đó complete rồi retry
			await this.exampleGenerationLocks.get(lockKey);
			// Sau khi lock trước complete, fetch examples mới nhất thay vì generate lại
			const wordRepository = this.getWordRepository();
			const latestExamples = await wordRepository.getExamplesByDefinition(
				definitionId,
				0,
				20
			);
			// Return examples cuối cùng (có thể đã được generate bởi request khác)
			return latestExamples.slice(-count);
		}

		// Tạo promise cho việc generate examples
		const generatePromise = this._doGenerateAndSaveExamples(wordId, definitionId, count);

		// Store promise vào locks map
		this.exampleGenerationLocks.set(lockKey, generatePromise);

		try {
			const result = await generatePromise;
			return result;
		} finally {
			// Clean up lock sau khi complete
			this.exampleGenerationLocks.delete(lockKey);
		}
	}

	private async _doGenerateAndSaveExamples(
		wordId: string,
		definitionId: string,
		count: number
	): Promise<Array<{ id: string; EN: string; VI: string }>> {
		const wordRepository = this.getWordRepository();

		// Get word details to extract term and language
		const word = await wordRepository.findById(wordId);
		if (!word) {
			throw new Error(`Word with ID ${wordId} not found`);
		}

		// Get existing examples for this definition to avoid duplicates
		const existingExamples = await wordRepository.getExamplesByDefinition(definitionId, 0, 20);

		// Check if we already have enough examples (có thể đã được generate bởi concurrent request)
		if (existingExamples.length >= 20) {
			return existingExamples.slice(-count);
		}

		// Generate new examples using LLM
		const llmService = await this.getLLMService();
		const neededCount = Math.min(count, 20 - existingExamples.length);

		if (neededCount <= 0) {
			return existingExamples.slice(-count);
		}

		const generatedExamples = await llmService.generateAdditionalExamples({
			term: word.term,
			source_language: word.language,
			target_language: word.language === Language.EN ? Language.VI : Language.EN,
			existingExamples: existingExamples.map((ex) => ({ EN: ex.EN, VI: ex.VI })),
			count: neededCount,
		});

		// Filter out examples that already exist in database
		const newExamples = this.filterDuplicateExamples(
			generatedExamples,
			existingExamples.map((ex) => ({ EN: ex.EN, VI: ex.VI }))
		);

		// If no new examples, return empty array
		if (newExamples.length === 0) {
			return [];
		}

		// Save only new examples to database
		await wordRepository.addExamplesToDefinition(definitionId, newExamples);

		// Fetch the newly saved examples to get their IDs
		const allExamples = await wordRepository.getExamplesByDefinition(definitionId, 0, 30);

		// Return the last generated examples (only the new ones)
		return allExamples.slice(-newExamples.length);
	}

	/**
	 * Filter out examples that already exist in the database
	 * @param generatedExamples - Examples generated by LLM
	 * @param existingExamples - Examples already in database
	 * @returns Array of truly new examples
	 */
	private filterDuplicateExamples(
		generatedExamples: Array<{ EN: string; VI: string }>,
		existingExamples: Array<{ EN: string; VI: string }>
	): Array<{ EN: string; VI: string }> {
		// Normalize text for comparison (lowercase, trim, remove extra spaces)
		const normalizeText = (text: string): string => {
			return text.toLowerCase().trim().replace(/\s+/g, ' ');
		};

		// Create set of existing examples for fast lookup
		const existingSet = new Set<string>();
		existingExamples.forEach((example) => {
			const normalizedKey = `${normalizeText(example.EN)}|${normalizeText(example.VI)}`;
			existingSet.add(normalizedKey);
		});

		// Filter out duplicates
		return generatedExamples.filter((example) => {
			const normalizedKey = `${normalizeText(example.EN)}|${normalizeText(example.VI)}`;
			return !existingSet.has(normalizedKey);
		});
	}
}
